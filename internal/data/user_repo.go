package data

import (
	"context"

	"github.com/go-kratos/kratos-layout/internal/domain"
	"github.com/go-kratos/kratos/v2/log"
)

// userRepo 实现 domain.UserRepo 接口
type userRepo struct {
	data *Data
	log  *log.Helper
}

// NewUserRepo 创建用户仓储实例
func NewUserRepo(data *Data, logger log.Logger) domain.UserRepo {
	return &userRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// Create 创建用户
func (r *userRepo) Create(ctx context.Context, u *domain.User) (*domain.User, error) {
	po, err := r.data.db.User.Create().
		SetAge(u.Age).
		SetName(u.Name).
		SetTenantID(u.TenantID).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return &domain.User{
		ID:       uint64(po.ID),
		Name:     po.Name,
		Age:      po.Age,
		TenantID: po.TenantID,
	}, nil
}

// Update 更新用户
func (r *userRepo) Update(ctx context.Context, u *domain.User) error {
	_, err := r.data.db.User.UpdateOneID(int(u.ID)).
		SetAge(u.Age).
		SetName(u.Name).
		Save(ctx)
	return err
}

// Delete 删除用户
func (r *userRepo) Delete(ctx context.Context, id uint64) error {
	return r.data.db.User.DeleteOneID(int(id)).Exec(ctx)
}

// List 获取用户列表
func (r *userRepo) List(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error) {
	total, err := r.data.db.User.Query().Count(ctx)
	if err != nil {
		return nil, 0, err
	}

	offset := (int(pageNum) - 1) * int(pageSize)
	pos, err := r.data.db.User.Query().
		Offset(offset).
		Limit(int(pageSize)).
		All(ctx)
	if err != nil {
		return nil, 0, err
	}

	var users []*domain.User
	for _, po := range pos {
		users = append(users, &domain.User{
			ID:       uint64(po.ID),
			Name:     po.Name,
			Age:      po.Age,
			TenantID: po.TenantID,
		})
	}
	return users, total, nil
}
