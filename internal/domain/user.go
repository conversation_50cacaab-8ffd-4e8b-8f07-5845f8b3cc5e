package domain

import (
	"context"
	"errors"
)

// User 领域模型
type User struct {
	ID       uint64
	Name     string
	Age      int
	TenantID uint64
}

// UserRepository 用户仓储接口
type UserRepo interface {
	Create(context.Context, *User) (*User, error)
	Update(context.Context, *User) error
	Delete(context.Context, uint64) error
	List(context.Context, int32, int32) ([]*User, int, error)
}

// UserUsecase 定义用例接口
type UserUsecase interface {
	Create(ctx context.Context, u *User) (*User, error)
	Update(ctx context.Context, u *User) error
	Delete(ctx context.Context, id uint64) error
	List(ctx context.Context, pageNum, pageSize int32) ([]*User, int, error)
}

var (
	ErrNameRequired = errors.New("name is required")
	ErrAgeMustBeGt0 = errors.New("age must be greater than 0")
)

func (u *User) Validate() error {
	if len(u.Name) == 0 {
		return ErrNameRequired
	}

	if u.Age < 0 {
		return ErrAgeMustBeGt0
	}
	return nil
}
